import os
import csv
import json
import time
import uuid
from datetime import datetime
import cv2
from pathlib import Path
import threading
from .accident_alert_manager import AccidentAlertManager
from .License_recognition.license_plate_recognizer import LicensePlateRecognizer

class ViolationManager:
    """
    Centralized manager for traffic violations (excluding accidents).
    Handles logging, image storage, and reporting.
    """
    
    def __init__(self, base_dir=None, stream_id="default", camera_location="Unknown", coordinates=None):
        self.stream_id = stream_id
        self.camera_location = camera_location
        
        # Ensure coordinates are always a properly formatted dictionary
        if coordinates is None:
            self.coordinates = {"lat": 0.0, "lng": 0.0}
        elif isinstance(coordinates, dict) and "lat" in coordinates and "lng" in coordinates:
            # Ensure values are proper floats
            try:
                self.coordinates = {
                    "lat": float(coordinates["lat"]), 
                    "lng": float(coordinates["lng"])
                }
            except (ValueError, TypeError):
                print(f"[{stream_id}] Warning: Invalid coordinate values. Using defaults.")
                self.coordinates = {"lat": 0.0, "lng": 0.0}
        else:
            print(f"[{stream_id}] Warning: Coordinates not in expected format. Using defaults.")
            self.coordinates = {"lat": 0.0, "lng": 0.0}

        self.lock = threading.Lock()  # Thread-safe operations
        
        # Counter for unique violation IDs
        self.violation_counter = 0
        
        # Setup directories
        if base_dir is None:
            base_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "violations"
        else:
            base_dir = Path(base_dir)
            
        self.base_dir = base_dir
        self._setup_directories()
        self._setup_logs()
        
        # Create accident alert manager for accident handling
        self.accident_alert_manager = AccidentAlertManager(
            stream_id=stream_id,
            camera_location=camera_location,
            coordinates=self.coordinates
        )
        
        # Initialize license plate recognizer
        self.license_plate_recognizer = LicensePlateRecognizer(base_dir=self.base_dir)
        
        print(f"[{self.stream_id}] Unified Violation Manager initialized for {camera_location}")
        
    def _setup_directories(self):
        """Create organized directory structure for violation snapshots"""
        # Main violations directory 
        self.base_dir.mkdir(exist_ok=True)
        
        # Create a single snapshots directory for all violation types
        self.snapshots_dir = self.base_dir / "snapshots"
        self.snapshots_dir.mkdir(exist_ok=True)
        
        # A single directory for all violation types with no date or camera subfolders
        self.violation_dirs = {}
        for violation_type in ["parking", "speed", "wrong_direction", 
                              "traffic_light", "helmet"]:
            # Instead of creating type-specific folders, just point all types to the same snapshots directory
            self.violation_dirs[violation_type] = self.snapshots_dir
        
        # Add plates directory - single directory with no date subfolders
        self.plates_dir = self.base_dir / "plates"
        self.plates_dir.mkdir(exist_ok=True)
        
    def _setup_logs(self):
        """Initialize centralized CSV and JSON log files"""
        # Use a single CSV file for all violations directly in violations folder
        self.csv_path = self.base_dir / "unified_violations.csv"
        if not self.csv_path.exists():
            with open(self.csv_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'Date', 'Time', 'License_Plate', 'Camera_ID', 
                    'Camera_Location', 'Violation_Type', 'Violation_ID',
                    'Vehicle_ID'
                ])
        
        # Use a single JSON file for all violations directly in violations folder
        self.json_path = self.base_dir / "unified_violations.json"
        if not self.json_path.exists():
            with open(self.json_path, 'w') as f:
                json.dump([], f)
    
    # Methods for recording different violation types
    
    def record_parking_violation(self, frame, vehicle_id, bbox, duration=None):
        """Record a parking violation"""
        return self._record_violation(frame, vehicle_id, bbox, "parking", 
                                     extra_info={"duration": duration})
    
    def record_speed_violation(self, frame, vehicle_id, bbox, speed=None):
        """Record a speed violation"""
        return self._record_violation(frame, vehicle_id, bbox, "speed", 
                                     extra_info={"speed": speed})
    
    def record_wrong_direction(self, frame, vehicle_id, bbox, line_pair=None):
        """Record a wrong direction violation"""
        return self._record_violation(frame, vehicle_id, bbox, "wrong_direction", 
                                     extra_info={"line_pair": line_pair})
    
    def record_traffic_violation(self, frame, vehicle_id, bbox, light_state=None):
        """Record a traffic light violation"""
        return self._record_violation(frame, vehicle_id, bbox, "traffic_light", 
                                     extra_info={"light_state": light_state})
    
    def record_accident(self, frame, vehicles_involved, bbox=None, accident_class=None):
        """
        Record a traffic accident - delegate to accident alert manager
        """
        # Just delegate to the accident alert manager
        return self.accident_alert_manager.record_accident(
            frame, vehicles_involved, bbox, accident_class
        )
    
    def record_helmet_violation(self, frame, vehicle_id, bbox, vehicle_type=None):
        """Record a helmet violation"""
        return self._record_violation(frame, vehicle_id, bbox, "helmet", 
                                     extra_info={"vehicle_type": vehicle_type})
    
    def _record_violation(self, frame, vehicle_id, bbox, violation_type, 
                         extra_info=None, save_full_frame=False):
        """
        Core method to record any type of violation
        Returns the violation ID and snapshot path
        """
        with self.lock:  # Thread-safe operation
            # Generate unique violation ID with counter and UUID to ensure uniqueness
            timestamp = datetime.now()
            date_str = timestamp.strftime('%Y-%m-%d')
            time_str = timestamp.strftime('%H:%M:%S')
            
            # Increment counter for each violation
            self.violation_counter += 1
            
            # Create simplified unique ID using counter and short UUID - no prefixes
            unique_id = f"{self.violation_counter:06d}_{uuid.uuid4().hex[:6]}"
            # Store the full violation type info in the record but use simple ID
            violation_id = unique_id
            
            # Save violation snapshot (only car closeup with wider margin for non-accidents)
            snapshot_path = self._save_violation_snapshot(
                frame, bbox, vehicle_id, violation_type, violation_id, save_full_frame)
            
            # Create violation record
            violation_record = {
                'date': date_str,
                'time': time_str,
                'license_plate': '',  # Empty for now, will be updated by recognition
                'camera_id': self.stream_id,
                'camera_location': self.camera_location,
                'location_coordinates': self.coordinates,
                'violation_type': violation_type,
                'violation_id': violation_id,
                'vehicle_id': vehicle_id,
                'snapshot_path': snapshot_path,
                'extra_info': extra_info or {}
            }
            
            # Update CSV log
            self._update_csv_log(violation_record)
            
            # Update JSON log
            self._update_json_log(violation_record)
            
            # Queue license plate recognition (async)
            # This will update the JSON record when complete
            self.license_plate_recognizer.process_violation(
                violation_record, 
                frame, 
                bbox, 
                self._update_plate_in_json
            )
            
            print(f"[{self.stream_id}] Recorded {violation_type} violation: {violation_id}")
            
            return violation_id, snapshot_path
    
    def _update_plate_in_json(self, updated_record):
        """
        Update an existing violation record in the JSON file with license plate info
        Called by the license plate recognizer when processing is complete
        """
        try:
            # Get the violation ID of the record to update
            violation_id = updated_record.get('violation_id')
            if not violation_id:
                print(f"[{self.stream_id}] Missing violation ID in update record")
                return

            # Read existing data
            with open(self.json_path, 'r') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError:
                    print(f"[{self.stream_id}] Error decoding JSON file")
                    return

            # Find and update the specific record - keep confidence out of JSON
            updated = False
            for i, record in enumerate(data):
                if record.get('violation_id') == violation_id:
                    # Update license plate fields
                    record['license_plate'] = updated_record.get('license_plate', '')

                    # Ensure plate snapshot path is included in the record
                    if 'plate_snapshot_path' in updated_record:
                        record['plate_snapshot_path'] = updated_record.get('plate_snapshot_path', '')

                    # Remove confidence from JSON if it exists
                    if 'plate_confidence' in record:
                        del record['plate_confidence']

                    updated = True
                    break

            if not updated:
                print(f"[{self.stream_id}] Violation record {violation_id} not found for plate update")
                return

            # Write updated data with atomic operation
            temp_file = str(self.json_path) + '.tmp'
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)

            # Replace the original file with the temp file
            os.replace(temp_file, self.json_path)

            # Update CSV file - now includes confidence
            self._update_plate_in_csv(violation_id, updated_record.get('license_plate', ''),
                                     updated_record.get('plate_confidence', 0.0))

            # Check if manual review is needed
            plate_text = updated_record.get('license_plate', '')
            confidence = updated_record.get('plate_confidence', 0.0)

            # Create manual review if license plate is empty or confidence is very low
            if not plate_text or confidence < 0.3:
                self._create_manual_review_for_violation(violation_id, updated_record, confidence)

            print(f"[{self.stream_id}] Updated license plate in records for {violation_id}: {updated_record.get('license_plate', '')}")
            print(f"[{self.stream_id}] Plate image path: {updated_record.get('plate_snapshot_path', 'Not available')}")

        except Exception as e:
            print(f"[{self.stream_id}] Error updating license plate in JSON: {str(e)}")

    def _create_manual_review_for_violation(self, violation_id, updated_record, confidence):
        """
        Create a manual review entry for a violation with failed or low-confidence license plate recognition
        """
        try:
            # Determine reason based on the failure type
            plate_text = updated_record.get('license_plate', '')
            if not plate_text:
                reason = "License plate recognition failed - no text detected"
            else:
                reason = f"License plate recognition low confidence ({confidence:.2f}) - manual verification needed"

            # Get the full violation record
            violations_data = []
            if os.path.exists(self.json_path):
                with open(self.json_path, 'r') as f:
                    violations_data = json.load(f)

            violation_data = None
            for violation in violations_data:
                if violation.get('violation_id') == violation_id:
                    violation_data = violation
                    break

            if not violation_data:
                print(f"[{self.stream_id}] Could not find violation data for manual review creation: {violation_id}")
                return

            # Create manual review record
            review_id = f"MR_{uuid.uuid4().hex[:8]}"
            review_record = {
                'review_id': review_id,
                'violation_id': violation_id,
                'violation_data': violation_data,
                'reason': reason,
                'notes': f"Automatic creation due to license plate recognition failure. Confidence: {confidence:.2f}",
                'license_plate': None,
                'reviewer_name': None,
                'created_at': datetime.now().isoformat(),
                'updated_at': None,
                'status': 'pending'
            }

            # Save manual review
            manual_reviews_path = os.path.join(self.base_dir, "manual_reviews.json")
            existing_reviews = []

            # Load existing reviews
            if os.path.exists(manual_reviews_path):
                try:
                    with open(manual_reviews_path, 'r') as f:
                        existing_reviews = json.load(f)
                except json.JSONDecodeError:
                    existing_reviews = []

            # Check if manual review already exists for this violation
            review_exists = any(review.get('violation_id') == violation_id for review in existing_reviews)
            if review_exists:
                print(f"[{self.stream_id}] Manual review already exists for violation: {violation_id}")
                return

            # Add new review
            existing_reviews.append(review_record)

            # Save to file
            temp_file = manual_reviews_path + '.tmp'
            with open(temp_file, 'w') as f:
                json.dump(existing_reviews, f, indent=2)

            os.replace(temp_file, manual_reviews_path)

            print(f"[{self.stream_id}] Created manual review {review_id} for violation {violation_id}")

        except Exception as e:
            print(f"[{self.stream_id}] Error creating manual review: {str(e)}")

    def get_manual_reviews(self, filter_status=None):
        """
        Get all manual reviews for this stream, optionally filtered by status
        """
        try:
            manual_reviews_path = os.path.join(self.base_dir, "manual_reviews.json")

            if not os.path.exists(manual_reviews_path):
                return []

            with open(manual_reviews_path, 'r') as f:
                reviews = json.load(f)

            # Filter by stream_id and optionally by status
            filtered_reviews = []
            for review in reviews:
                violation_data = review.get('violation_data', {})
                if violation_data.get('camera_id') == self.stream_id:
                    if filter_status is None or review.get('status') == filter_status:
                        filtered_reviews.append(review)

            return filtered_reviews

        except Exception as e:
            print(f"[{self.stream_id}] Error getting manual reviews: {str(e)}")
            return []

    def update_manual_review(self, review_id, license_plate=None, notes=None, reviewer_name=None):
        """
        Update a manual review with corrected license plate information
        """
        try:
            manual_reviews_path = os.path.join(self.base_dir, "manual_reviews.json")

            if not os.path.exists(manual_reviews_path):
                return False, "Manual reviews file not found"

            with open(manual_reviews_path, 'r') as f:
                reviews = json.load(f)

            # Find and update the review
            review_found = False
            for review in reviews:
                if review.get('review_id') == review_id:
                    review_found = True

                    # Update fields
                    if license_plate is not None:
                        review['license_plate'] = license_plate
                    if notes is not None:
                        review['notes'] = notes
                    if reviewer_name is not None:
                        review['reviewer_name'] = reviewer_name

                    review['updated_at'] = datetime.now().isoformat()

                    # Update status to completed if license plate is provided
                    if license_plate:
                        review['status'] = 'completed'

                        # Also update the original violation record
                        violation_id = review.get('violation_id')
                        if violation_id:
                            self._update_violation_with_manual_plate(violation_id, license_plate)

                    break

            if not review_found:
                return False, "Manual review not found"

            # Save updated reviews
            temp_file = manual_reviews_path + '.tmp'
            with open(temp_file, 'w') as f:
                json.dump(reviews, f, indent=2)

            os.replace(temp_file, manual_reviews_path)

            print(f"[{self.stream_id}] Updated manual review {review_id}")
            return True, None

        except Exception as e:
            print(f"[{self.stream_id}] Error updating manual review: {str(e)}")
            return False, f"Error updating manual review: {str(e)}"

    def _update_violation_with_manual_plate(self, violation_id, license_plate):
        """
        Update the original violation record with manually corrected license plate
        """
        try:
            # Update JSON file
            if os.path.exists(self.json_path):
                with open(self.json_path, 'r') as f:
                    violations = json.load(f)

                # Find and update the violation
                for violation in violations:
                    if violation.get('violation_id') == violation_id:
                        violation['license_plate'] = license_plate
                        violation['manual_review_completed'] = True
                        violation['manual_review_date'] = datetime.now().isoformat()
                        break

                # Save updated violations
                temp_file = self.json_path + '.tmp'
                with open(temp_file, 'w') as f:
                    json.dump(violations, f, indent=2)

                os.replace(temp_file, self.json_path)

            # Update CSV file
            self._update_plate_in_csv(violation_id, license_plate, 1.0)  # Set confidence to 1.0 for manual entry

            print(f"[{self.stream_id}] Updated violation {violation_id} with manual license plate: {license_plate}")

        except Exception as e:
            print(f"[{self.stream_id}] Error updating violation with manual plate: {str(e)}")

    def delete_manual_review(self, review_id):
        """
        Delete a manual review
        """
        try:
            manual_reviews_path = os.path.join(self.base_dir, "manual_reviews.json")

            if not os.path.exists(manual_reviews_path):
                return False, "Manual reviews file not found"

            with open(manual_reviews_path, 'r') as f:
                reviews = json.load(f)

            # Remove the review
            original_count = len(reviews)
            reviews = [review for review in reviews if review.get('review_id') != review_id]

            if len(reviews) == original_count:
                return False, "Manual review not found"

            # Save updated reviews
            temp_file = manual_reviews_path + '.tmp'
            with open(temp_file, 'w') as f:
                json.dump(reviews, f, indent=2)

            os.replace(temp_file, manual_reviews_path)

            print(f"[{self.stream_id}] Deleted manual review {review_id}")
            return True, None

        except Exception as e:
            print(f"[{self.stream_id}] Error deleting manual review: {str(e)}")
            return False, f"Error deleting manual review: {str(e)}"
    
    def _update_plate_in_csv(self, violation_id, plate_text, confidence=0.0):
        """
        Update the CSV file with the license plate text and confidence
        Note: This is a simple implementation - a production system might use a database
        """
        try:
            # Read all CSV data
            rows = []
            with open(self.csv_path, 'r', newline='') as f:
                reader = csv.reader(f)
                rows = list(reader)
            
            # Check if confidence column exists in header
            header = rows[0]
            has_confidence_col = 'Plate_Confidence' in header
            
            # Add confidence column if it doesn't exist
            confidence_idx = -1
            if not has_confidence_col:
                header.append('Plate_Confidence')
                confidence_idx = len(header) - 1
                rows[0] = header
            else:
                confidence_idx = header.index('Plate_Confidence')
            
            # Find and update the record (search for violation ID in column 6)
            updated = False
            for i, row in enumerate(rows):
                if i > 0 and len(row) > 6 and row[6] == violation_id:
                    row[2] = plate_text  # License plate is in column 3 (index 2)
                    
                    # Make sure row is long enough for confidence
                    while len(row) <= confidence_idx:
                        row.append('')
                    
                    # Update confidence
                    row[confidence_idx] = f"{confidence:.4f}"
                    updated = True
                    break
            
            if updated:
                # Write back the updated data
                with open(self.csv_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerows(rows)
        
        except Exception as e:
            print(f"[{self.stream_id}] Error updating license plate in CSV: {str(e)}")
    
    def _save_violation_snapshot(self, frame, bbox, vehicle_id, violation_type, violation_id, save_full_frame=False):
        """Save a snapshot of the violation - focused on vehicle with wider margin"""
        try:
            # Generate filename with timestamp
            timestamp_str = datetime.now().strftime('%H%M%S')
            # All violations now go to the same snapshots directory
            
            # For violations where we want full frame, save it
            if save_full_frame:
                full_path = self.snapshots_dir / f"{violation_type}_full_{violation_id}_{timestamp_str}.jpg"
                cv2.imwrite(str(full_path), frame)
                return str(full_path.relative_to(self.base_dir))
            
            # For all other violations, save the cropped vehicle with reduced margin
            if bbox is not None:
                # Extract vehicle with margin
                x1, y1, x2, y2 = bbox
                # Calculate expanded box with 45% margin (reduced by 15% from previous 60%)
                width, height = x2 - x1, y2 - y1
                margin_x, margin_y = int(width * 0.45), int(height * 0.45)
                
                # Ensure expanded box stays within frame bounds
                h, w = frame.shape[:2]
                ex1 = max(0, x1 - margin_x)
                ey1 = max(0, y1 - margin_y)
                ex2 = min(w-1, x2 + margin_x)
                ey2 = min(h-1, y2 + margin_y)
                
                if ex1 < ex2 and ey1 < ey2:  # Valid box
                    vehicle_img = frame[ey1:ey2, ex1:ex2]
                    
                    # Add violation information to image with thinner font
                    # Use violation ID for simpler display
                    violation_text = f"{violation_type.upper()} VIOLATION - ID: {violation_id}"
                    cv2.putText(vehicle_img, violation_text, 
                              (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)  # Font thickness set to 1
                    
                    # Save vehicle closeup image - use simplified ID in filename
                    # All violation types go into the same directory now
                    closeup_path = self.snapshots_dir / f"{violation_type}_vehicle_{violation_id}_{timestamp_str}.jpg"
                    cv2.imwrite(str(closeup_path), vehicle_img)
                    return str(closeup_path.relative_to(self.base_dir))
        
            # Fallback if bbox is invalid - save full frame
            full_path = self.snapshots_dir / f"{violation_type}_fallback_{violation_id}_{timestamp_str}.jpg"
            cv2.imwrite(str(full_path), frame)
            return str(full_path.relative_to(self.base_dir))
        
        except Exception as e:
            print(f"[{self.stream_id}] Error saving violation snapshot: {str(e)}")
            # Return a placeholder path if we couldn't save the image
            return f"error_snapshot_{violation_type}_{violation_id}_{timestamp_str}"
        
    def _update_csv_log(self, violation_record):
        """Update the CSV log with new violation"""
        try:
            with open(self.csv_path, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    violation_record['date'],
                    violation_record['time'],
                    violation_record['license_plate'],
                    violation_record['camera_id'],
                    violation_record['camera_location'],
                    violation_record['violation_type'],
                    violation_record['violation_id'],
                    violation_record['vehicle_id']
                    # Coordinates are only added to JSON, not CSV
                ])
        except Exception as e:
            print(f"[{self.stream_id}] Error updating CSV log: {str(e)}")
    
    def _update_json_log(self, violation_record):
        """Update the unified JSON log with new violation"""
        try:
            # Read existing data
            with open(self.json_path, 'r') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError:
                    # Handle empty or invalid JSON file
                    data = []
            
            # Create a copy of the record without vehicle_id for JSON output
            sanitized_record = violation_record.copy()
            if 'vehicle_id' in sanitized_record:
                del sanitized_record['vehicle_id']  # Remove vehicle_id from JSON
            
            # Remove confidence info from JSON - it will only be in CSV
            if 'plate_confidence' in sanitized_record:
                del sanitized_record['plate_confidence']
            
            # Make sure plate_snapshot_path is initialized
            if 'plate_snapshot_path' not in sanitized_record:
                sanitized_record['plate_snapshot_path'] = ""
            
            # Add the sanitized record
            data.append(sanitized_record)
            
            # Write updated data with atomic operation
            temp_file = str(self.json_path) + '.tmp'
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
                
            # Replace the original file with the temp file
            os.replace(temp_file, self.json_path)
            
        except Exception as e:
            print(f"[{self.stream_id}] Error updating JSON log: {str(e)}")
    
    def set_camera_coordinates(self, latitude, longitude):
        """Update the camera coordinates for mapping"""
        try:
            lat = float(latitude)
            lng = float(longitude)
            
            # Log the coordinate change
            print(f"[{self.stream_id}] Camera coordinates updated: {lat}, {lng}")
            
            self.coordinates = {"lat": lat, "lng": lng}
            
            # Also update coordinates in accident alert manager
            if hasattr(self, 'accident_alert_manager'):
                self.accident_alert_manager.set_camera_coordinates(latitude, longitude)
        except (ValueError, TypeError) as e:
            print(f"[{self.stream_id}] Invalid coordinates provided ({e}), using defaults")
            self.coordinates = {"lat": 0.0, "lng": 0.0}

        return self.coordinates
