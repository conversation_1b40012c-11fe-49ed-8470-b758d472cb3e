#!/usr/bin/env python3
"""
Test script for manual review functionality
This script tests the manual review API endpoints
"""

import requests
import json
import time
from datetime import datetime

# API base URL
BASE_URL = "http://localhost:8000/api"

def test_manual_review_endpoints():
    """Test all manual review endpoints"""
    print("Testing Manual Review API Endpoints")
    print("=" * 50)
    
    # Test 1: Get all manual reviews (should be empty initially)
    print("\n1. Testing GET /api/manual-reviews")
    try:
        response = requests.get(f"{BASE_URL}/manual-reviews")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            reviews = response.json()
            print(f"Found {len(reviews)} manual reviews")
            for review in reviews:
                print(f"  - Review ID: {review.get('review_id')}")
                print(f"    Violation ID: {review.get('violation_id')}")
                print(f"    Status: {review.get('status')}")
                print(f"    Reason: {review.get('reason')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Test 2: Get all violations to find one for manual review
    print("\n2. Testing GET /api/violations to find violations")
    try:
        response = requests.get(f"{BASE_URL}/violations")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            violations = response.json()
            print(f"Found {len(violations)} violations")
            
            # Find a violation with empty license plate for testing
            test_violation = None
            for violation in violations:
                if not violation.get('license_plate') or violation.get('license_plate').strip() == '':
                    test_violation = violation
                    break
            
            if test_violation:
                print(f"Found violation for testing: {test_violation.get('violation_id')}")
                
                # Test 3: Create manual review
                print("\n3. Testing POST /api/manual-reviews")
                manual_review_data = {
                    "violation_id": test_violation.get('violation_id'),
                    "reason": "Test manual review creation",
                    "notes": "This is a test manual review created by the test script"
                }
                
                try:
                    response = requests.post(f"{BASE_URL}/manual-reviews", json=manual_review_data)
                    print(f"Status: {response.status_code}")
                    if response.status_code == 200:
                        created_review = response.json()
                        review_id = created_review.get('review_id')
                        print(f"Created manual review: {review_id}")
                        
                        # Test 4: Get specific manual review
                        print(f"\n4. Testing GET /api/manual-reviews/{review_id}")
                        try:
                            response = requests.get(f"{BASE_URL}/manual-reviews/{review_id}")
                            print(f"Status: {response.status_code}")
                            if response.status_code == 200:
                                review = response.json()
                                print(f"Retrieved review: {review.get('review_id')}")
                                print(f"Status: {review.get('status')}")
                            else:
                                print(f"Error: {response.text}")
                        except Exception as e:
                            print(f"Error: {str(e)}")
                        
                        # Test 5: Update manual review
                        print(f"\n5. Testing PUT /api/manual-reviews/{review_id}")
                        update_data = {
                            "license_plate": "TEST123",
                            "notes": "Updated by test script",
                            "reviewer_name": "Test Reviewer"
                        }

                        try:
                            response = requests.put(f"{BASE_URL}/manual-reviews/{review_id}", json=update_data)
                            print(f"Status: {response.status_code}")
                            if response.status_code == 200:
                                updated_review = response.json()
                                print(f"Updated review: {updated_review.get('review_id')}")
                                print(f"License plate: {updated_review.get('license_plate')}")
                                print(f"Status: {updated_review.get('status')}")

                                # Test 5.1: Verify the original violation was updated
                                print(f"\n5.1. Verifying original violation was updated")
                                violation_id = test_violation.get('violation_id')
                                try:
                                    response = requests.get(f"{BASE_URL}/violations/{violation_id}")
                                    if response.status_code == 200:
                                        updated_violation = response.json()
                                        print(f"Original violation license plate: {updated_violation.get('license_plate')}")
                                        print(f"Manual review completed: {updated_violation.get('manual_review_completed')}")
                                        print(f"Manual review date: {updated_violation.get('manual_review_date')}")

                                        if updated_violation.get('license_plate') == "TEST123":
                                            print("✅ SUCCESS: Original violation was properly updated!")
                                        else:
                                            print("❌ FAILED: Original violation was not updated properly")
                                    else:
                                        print(f"Error getting updated violation: {response.text}")
                                except Exception as e:
                                    print(f"Error verifying violation update: {str(e)}")

                            else:
                                print(f"Error: {response.text}")
                        except Exception as e:
                            print(f"Error: {str(e)}")
                        
                        # Test 6: Test media endpoints
                        print(f"\n6. Testing media endpoints for manual review {review_id}")

                        # Test violation snapshot
                        try:
                            response = requests.get(f"{BASE_URL}/media/manual-reviews/{review_id}/violation")
                            print(f"Violation snapshot status: {response.status_code}")
                            if response.status_code == 200:
                                print("✅ Violation snapshot accessible")
                            else:
                                print(f"❌ Violation snapshot error: {response.text}")
                        except Exception as e:
                            print(f"Error accessing violation snapshot: {str(e)}")

                        # Test plate snapshot
                        try:
                            response = requests.get(f"{BASE_URL}/media/manual-reviews/{review_id}/plate")
                            print(f"Plate snapshot status: {response.status_code}")
                            if response.status_code == 200:
                                print("✅ Plate snapshot accessible")
                            else:
                                print(f"❌ Plate snapshot error: {response.text}")
                        except Exception as e:
                            print(f"Error accessing plate snapshot: {str(e)}")

                        # Test 7: Get manual review statistics
                        print("\n7. Testing GET /api/manual-reviews/statistics")
                        try:
                            response = requests.get(f"{BASE_URL}/manual-reviews/statistics")
                            print(f"Status: {response.status_code}")
                            if response.status_code == 200:
                                stats = response.json()
                                print(f"Statistics: {json.dumps(stats, indent=2)}")
                            else:
                                print(f"Error: {response.text}")
                        except Exception as e:
                            print(f"Error: {str(e)}")
                        
                        # Test 8: Delete manual review
                        print(f"\n8. Testing DELETE /api/manual-reviews/{review_id}")
                        try:
                            response = requests.delete(f"{BASE_URL}/manual-reviews/{review_id}")
                            print(f"Status: {response.status_code}")
                            if response.status_code == 200:
                                result = response.json()
                                print(f"Deleted: {result.get('message')}")
                                print("✅ Manual review deleted successfully")
                            else:
                                print(f"Error: {response.text}")
                        except Exception as e:
                            print(f"Error: {str(e)}")
                        
                    else:
                        print(f"Error creating manual review: {response.text}")
                except Exception as e:
                    print(f"Error: {str(e)}")
            else:
                print("No violations with empty license plates found for testing")
                
                # Create a test manual review anyway
                print("\n3. Creating test manual review with dummy violation ID")
                manual_review_data = {
                    "violation_id": "TEST_VIOLATION_001",
                    "reason": "Test manual review creation",
                    "notes": "This is a test manual review created by the test script"
                }
                
                try:
                    response = requests.post(f"{BASE_URL}/manual-reviews", json=manual_review_data)
                    print(f"Status: {response.status_code}")
                    print(f"Response: {response.text}")
                except Exception as e:
                    print(f"Error: {str(e)}")
        else:
            print(f"Error getting violations: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")

def test_api_connection():
    """Test if API server is running"""
    print("Testing API Connection")
    print("=" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}")
        print(f"API Status: {response.status_code}")
        if response.status_code == 200:
            print("API is running!")
            return True
        else:
            print("API returned error")
            return False
    except Exception as e:
        print(f"Cannot connect to API: {str(e)}")
        print("Make sure the API server is running on http://localhost:8000")
        return False

if __name__ == "__main__":
    print("Manual Review API Test Script")
    print("=" * 40)
    print(f"Testing API at: {BASE_URL}")
    print(f"Time: {datetime.now().isoformat()}")
    
    # Test API connection first
    if test_api_connection():
        # Run manual review tests
        test_manual_review_endpoints()
    
    print("\nTest completed!")
