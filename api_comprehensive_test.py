#!/usr/bin/env python3
"""
Comprehensive API Test Suite
Tests all API endpoints automatically and validates responses
"""

import requests
import json
import uuid
import time
import os
from datetime import datetime
from typing import Dict, List, Any

# Configuration
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api"

class APITester:
    def __init__(self):
        self.test_results = []
        self.test_data = {}
        
    def log_test(self, endpoint: str, method: str, status_code: int, expected: int, details: str = ""):
        """Log test result"""
        success = status_code == expected
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "expected": expected,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {method} {endpoint} - {status_code} (expected {expected}) {details}")
        
    def test_endpoint(self, method: str, endpoint: str, expected_status: int = 200, 
                     data: Dict = None, params: Dict = None) -> requests.Response:
        """Test a single endpoint"""
        url = f"{API_URL}{endpoint}"
        
        try:
            if method == "GET":
                response = requests.get(url, params=params)
            elif method == "POST":
                response = requests.post(url, json=data)
            elif method == "PUT":
                response = requests.put(url, json=data)
            elif method == "DELETE":
                response = requests.delete(url)
            else:
                raise ValueError(f"Unsupported method: {method}")
                
            self.log_test(endpoint, method, response.status_code, expected_status)
            return response
            
        except Exception as e:
            self.log_test(endpoint, method, 0, expected_status, f"Error: {str(e)}")
            return None

    def test_api_root(self):
        """Test API root endpoint"""
        print("\n🔍 Testing API Root")
        response = self.test_endpoint("GET", "")
        if response and response.status_code == 200:
            data = response.json()
            assert "message" in data, "API root should return message"

    def test_video_sources(self):
        """Test video source management endpoints"""
        print("\n🎥 Testing Video Source Management")
        
        # Test list sources (should work even if empty)
        self.test_endpoint("GET", "/sources")
        
        # Test create video source
        test_source = {
            "id": f"test_source_{uuid.uuid4().hex[:6]}",
            "name": "Test Video Source",
            "source": "test_video.mp4",
            "use_stream": False,
            "location": "Test Location",
            "coordinates": {"lat": 0.0, "lng": 0.0},
            "speed_limit": 60.0
        }
        
        # This might fail if file doesn't exist, expect 404
        response = self.test_endpoint("POST", "/sources", 404, test_source)
        
        # Test with a stream URL (should work)
        stream_source = {
            "id": f"test_stream_{uuid.uuid4().hex[:6]}",
            "name": "Test Stream Source", 
            "source": "https://example.com/stream",
            "use_stream": True,
            "location": "Test Stream Location",
            "coordinates": {"lat": 1.0, "lng": 1.0},
            "speed_limit": 50.0
        }
        
        response = self.test_endpoint("POST", "/sources", 200, stream_source)
        if response and response.status_code == 200:
            source_data = response.json()
            source_id = source_data.get("id")
            self.test_data["source_id"] = source_id
            
            # Test get specific source
            self.test_endpoint("GET", f"/sources/{source_id}")
            
            # Test source control endpoints
            self.test_endpoint("POST", f"/sources/{source_id}/start", 500)  # Might fail without actual video
            self.test_endpoint("POST", f"/sources/{source_id}/stop", 200)   # Stop might succeed even if start failed
            
            # Test area configuration
            self.test_endpoint("GET", f"/sources/{source_id}/areas")
            
            test_areas = {
                "speed_zones": [{"points": [[0, 0], [100, 0], [100, 100], [0, 100]]}],
                "parking_zones": [{"points": [[200, 200], [300, 200], [300, 300], [200, 300]]}]
            }
            self.test_endpoint("PUT", f"/sources/{source_id}/areas", 200, test_areas)
            
            # Test clear areas
            self.test_endpoint("POST", f"/sources/{source_id}/areas/clear-all")
            
            # Test settings
            self.test_endpoint("GET", f"/sources/{source_id}/settings")
            
            test_settings = {
                "models": {
                    "accident_detection": True,
                    "helmet_detection": False,
                    "traffic_violation": True,
                    "speed_detection": True
                }
            }
            self.test_endpoint("PUT", f"/sources/{source_id}/settings", 200, test_settings)
            
            # Test frame extraction
            self.test_endpoint("GET", f"/sources/{source_id}/frame", 500)  # Might fail without video
            
            # Test delete source
            self.test_endpoint("DELETE", f"/sources/{source_id}")

    def test_violations(self):
        """Test violation endpoints"""
        print("\n🚨 Testing Violation Endpoints")
        
        # Test get all violations
        self.test_endpoint("GET", "/violations")
        
        # Test filtered violations
        filter_data = {
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "violation_type": "speed",
            "source_id": "test_camera"
        }
        self.test_endpoint("POST", "/violations", 200, filter_data)
        
        # Test get violations by type
        self.test_endpoint("GET", "/violations/types/speed")
        
        # Test get specific violation (will likely return 404)
        self.test_endpoint("GET", "/violations/test_violation_123", 404)

    def test_manual_reviews(self):
        """Test manual review endpoints"""
        print("\n📝 Testing Manual Review Endpoints")
        
        # Test get all manual reviews
        self.test_endpoint("GET", "/manual-reviews")
        
        # Test create manual review
        test_violation_data = {
            "date": datetime.now().strftime('%Y-%m-%d'),
            "time": datetime.now().strftime('%H:%M:%S'),
            "license_plate": "",
            "camera_id": "test_camera",
            "camera_location": "Test Location",
            "violation_type": "speed",
            "violation_id": f"TEST_VIO_{uuid.uuid4().hex[:6]}",
            "snapshot_path": "snapshots/test.jpg",
            "plate_snapshot_path": "plates/test.jpg"
        }
        
        review_data = {
            "violation_data": test_violation_data,
            "reason": "Test manual review",
            "notes": "Created by API test"
        }
        
        response = self.test_endpoint("POST", "/manual-reviews", 200, review_data)
        if response and response.status_code == 200:
            review = response.json()
            review_id = review.get("review_id")
            self.test_data["review_id"] = review_id
            
            # Test get specific manual review
            self.test_endpoint("GET", f"/manual-reviews/{review_id}")
            
            # Test update manual review
            update_data = {
                "license_plate": "TEST123",
                "notes": "Updated by API test",
                "reviewer_name": "API Tester"
            }
            self.test_endpoint("PUT", f"/manual-reviews/{review_id}", 200, update_data)
            
            # Test media endpoints (will likely fail without actual files)
            self.test_endpoint("GET", f"/media/manual-reviews/{review_id}/plate", 404)
            self.test_endpoint("GET", f"/media/manual-reviews/{review_id}/violation", 404)
            
            # Test delete manual review
            self.test_endpoint("DELETE", f"/manual-reviews/{review_id}")

    def test_accidents(self):
        """Test accident endpoints"""
        print("\n🚑 Testing Accident Endpoints")
        
        # Test filtered accidents
        filter_data = {
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "source_id": "test_camera"
        }
        self.test_endpoint("POST", "/accidents", 200, filter_data)
        
        # Test accident statistics
        self.test_endpoint("GET", "/accidents/statistics", 200)

    def test_media_endpoints(self):
        """Test media access endpoints"""
        print("\n🖼️ Testing Media Endpoints")
        
        # Test violation media (will likely return 404)
        self.test_endpoint("GET", "/media/violations/test_violation_123", 404)
        self.test_endpoint("GET", "/media/plates/test_violation_123", 404)
        
        # Test accident media (will likely return 404)
        self.test_endpoint("GET", "/media/accidents/test_accident_123", 404)

    def test_system_endpoints(self):
        """Test system management endpoints"""
        print("\n⚙️ Testing System Endpoints")

        # Test system status
        response = self.test_endpoint("GET", "/system/status")
        if response and response.status_code == 200:
            status = response.json()
            assert "active_sources" in status, "System status should include active_sources"
            assert "active_connections" in status, "System status should include active_connections"

    def test_edge_cases(self):
        """Test edge cases and error handling"""
        print("\n🔍 Testing Edge Cases & Error Handling")

        # Test non-existent endpoints
        self.test_endpoint("GET", "/nonexistent", 404)
        self.test_endpoint("POST", "/invalid/endpoint", 405)  # Method not allowed

        # Test invalid source IDs
        self.test_endpoint("GET", "/sources/invalid_source_123", 404)
        self.test_endpoint("POST", "/sources/invalid_source_123/start", 404)
        self.test_endpoint("DELETE", "/sources/invalid_source_123", 404)

        # Test invalid violation IDs
        self.test_endpoint("GET", "/violations/invalid_violation_123", 404)

        # Test invalid manual review IDs
        self.test_endpoint("GET", "/manual-reviews/invalid_review_123", 404)
        self.test_endpoint("PUT", "/manual-reviews/invalid_review_123", 404, {"license_plate": "TEST"})
        self.test_endpoint("DELETE", "/manual-reviews/invalid_review_123", 404)

        # Test invalid media requests
        self.test_endpoint("GET", "/media/violations/invalid_123", 404)
        self.test_endpoint("GET", "/media/plates/invalid_123", 404)
        self.test_endpoint("GET", "/media/accidents/invalid_123", 404)

        # Test malformed JSON data
        print("   Testing malformed requests...")
        try:
            # Test with invalid JSON structure for manual review
            invalid_review = {
                "violation_data": "invalid_structure",  # Should be dict
                "reason": 123  # Should be string
            }
            self.test_endpoint("POST", "/manual-reviews", 422, invalid_review)

            # Test with missing required fields
            incomplete_source = {
                "id": "test_incomplete",
                # Missing required fields
            }
            self.test_endpoint("POST", "/sources", 422, incomplete_source)

        except Exception as e:
            print(f"   Error testing malformed requests: {str(e)}")

    def test_data_validation(self):
        """Test data validation and constraints"""
        print("\n✅ Testing Data Validation")

        # Test source creation with invalid data types
        invalid_sources = [
            {
                "id": 123,  # Should be string
                "name": "Test",
                "source": "test.mp4",
                "use_stream": False,
                "location": "Test",
                "coordinates": {"lat": 0.0, "lng": 0.0},
                "speed_limit": 60.0
            },
            {
                "id": "test",
                "name": "Test",
                "source": "test.mp4",
                "use_stream": "invalid",  # Should be boolean
                "location": "Test",
                "coordinates": {"lat": 0.0, "lng": 0.0},
                "speed_limit": 60.0
            },
            {
                "id": "test",
                "name": "Test",
                "source": "test.mp4",
                "use_stream": False,
                "location": "Test",
                "coordinates": "invalid",  # Should be dict
                "speed_limit": 60.0
            }
        ]

        for i, invalid_source in enumerate(invalid_sources):
            # Some validation might return 404 if file doesn't exist, others 422 for validation
            expected_status = 422 if i > 0 else 404  # First one might be file not found
            self.test_endpoint("POST", "/sources", expected_status, invalid_source)

    def test_performance_endpoints(self):
        """Test endpoints that might have performance implications"""
        print("\n⚡ Testing Performance-Critical Endpoints")

        # Test endpoints that might return large datasets
        start_time = time.time()
        response = self.test_endpoint("GET", "/violations")
        end_time = time.time()

        if response:
            response_time = end_time - start_time
            print(f"   Violations endpoint response time: {response_time:.2f}s")

            if response.status_code == 200:
                data = response.json()
                print(f"   Returned {len(data)} violations")

        # Test accidents endpoint
        start_time = time.time()
        response = self.test_endpoint("POST", "/accidents", 200, {})
        end_time = time.time()

        if response:
            response_time = end_time - start_time
            print(f"   Accidents endpoint response time: {response_time:.2f}s")

            if response.status_code == 200:
                data = response.json()
                print(f"   Returned {len(data)} accidents")

    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Comprehensive API Test Suite")
        print("=" * 60)
        
        # Check if API is running
        try:
            response = requests.get(f"{BASE_URL}/api")
            if response.status_code != 200:
                print("❌ API is not running or not accessible")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to API: {str(e)}")
            print("Please start the API server first: python api_server.py")
            return False
        
        print("✅ API is accessible, starting tests...")
        
        # Run all test categories
        self.test_api_root()
        self.test_video_sources()
        self.test_violations()
        self.test_manual_reviews()
        self.test_accidents()
        self.test_media_endpoints()
        self.test_system_endpoints()
        self.test_edge_cases()
        self.test_data_validation()
        self.test_performance_endpoints()
        
        # Print summary
        self.print_summary()
        return True

    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   {result['method']} {result['endpoint']} - {result['status_code']} {result['details']}")
        
        # Save detailed results to file
        with open("api_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\n📄 Detailed results saved to: api_test_results.json")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
