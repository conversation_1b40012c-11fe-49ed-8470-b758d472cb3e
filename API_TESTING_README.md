# API Testing Suite

This directory contains comprehensive testing tools for the Traffic Monitoring API. Instead of manually testing endpoints through documentation, these automated tests validate all API functionality.

## 📁 Test Files Overview

### 🚀 Quick Tests
**File:** `api_quick_test.py`
- **Purpose:** Fast validation of core API functionality
- **Duration:** ~30 seconds
- **Use Case:** Quick health check, CI/CD pipeline, daily validation

### 🔍 Comprehensive Tests  
**File:** `api_comprehensive_test.py`
- **Purpose:** Complete testing of all API endpoints
- **Duration:** ~2-5 minutes
- **Use Case:** Full API validation, regression testing, release validation

### 📝 Manual Review Tests
**File:** `simple_manual_review_test.py`
- **Purpose:** Focused testing of manual review workflow
- **Duration:** ~1 minute
- **Use Case:** Manual review feature validation

### 🎛️ Test Runner
**File:** `run_api_tests.py`
- **Purpose:** Unified test runner with options
- **Use Case:** Run multiple test suites with single command

## 🚀 Quick Start

### Prerequisites
1. **Start the API server:**
   ```bash
   python api_server.py
   ```
   
2. **Verify API is running:**
   - Open browser: http://localhost:8000/api
   - Should see: `{"message": "Traffic Monitoring API is running"}`

### Run Tests

#### Option 1: Quick Test (Recommended for daily use)
```bash
python api_quick_test.py
```

#### Option 2: Comprehensive Test (Recommended for releases)
```bash
python api_comprehensive_test.py
```

#### Option 3: Manual Review Only
```bash
python simple_manual_review_test.py
```

#### Option 4: Use Test Runner (All options)
```bash
# Quick tests only
python run_api_tests.py --quick

# Comprehensive tests only  
python run_api_tests.py --comprehensive

# Manual review tests only
python run_api_tests.py --manual-review

# All tests
python run_api_tests.py --all
```

## 📊 Test Coverage

### Core Endpoints Tested
- ✅ **API Root** - Basic connectivity
- ✅ **Video Sources** - CRUD operations, control, settings
- ✅ **Violations** - Data retrieval, filtering
- ✅ **Manual Reviews** - Complete CRUD workflow
- ✅ **Accidents** - Data retrieval, statistics
- ✅ **Media Access** - Image/snapshot endpoints
- ✅ **System Status** - Health monitoring

### Test Categories
- ✅ **Functionality Tests** - Core feature validation
- ✅ **Error Handling** - Invalid requests, edge cases
- ✅ **Data Validation** - Input validation, constraints
- ✅ **Performance** - Response time monitoring
- ✅ **Workflow Tests** - End-to-end scenarios

## 📈 Understanding Test Results

### Quick Test Output
```
🚀 Quick API Test Suite
==================================================
🔍 Testing API Status...
✅ API is running

🎯 Testing Core Endpoints...
✅ GET  - 200 (API Root)
✅ GET /sources - 200 (List Video Sources)
✅ GET /violations - 200 (Get Violations)
...

📝 Testing Manual Review Workflow...
   1. Creating manual review...
   ✅ Created manual review: MR_abc123
   2. Retrieving manual review...
   ✅ Retrieved manual review successfully
   ...

📊 QUICK TEST SUMMARY
==================================================
Core Endpoint Tests: 8/8 passed
✅ All core endpoints are working correctly!
```

### Comprehensive Test Output
```
🚀 Starting Comprehensive API Test Suite
============================================================
✅ API is accessible, starting tests...

🔍 Testing API Root
✅ PASS GET  - 200 (expected 200) 

🎥 Testing Video Source Management
✅ PASS GET /sources - 200 (expected 200) 
❌ FAIL POST /sources - 404 (expected 404) 
...

📊 TEST SUMMARY
============================================================
Total Tests: 45
✅ Passed: 42
❌ Failed: 3
Success Rate: 93.3%
```

## 🔧 Customizing Tests

### Adding New Test Cases
Edit the test files to add new endpoints or test scenarios:

```python
# In api_comprehensive_test.py
def test_new_feature(self):
    """Test new API feature"""
    print("\n🆕 Testing New Feature")
    
    # Test new endpoint
    response = self.test_endpoint("GET", "/new-endpoint")
    if response and response.status_code == 200:
        data = response.json()
        # Add assertions here
```

### Modifying Expected Results
Update expected status codes for different scenarios:

```python
# Expect 404 for missing resources
self.test_endpoint("GET", "/violations/nonexistent", 404)

# Expect 422 for validation errors
self.test_endpoint("POST", "/sources", 422, invalid_data)
```

## 🐛 Troubleshooting

### Common Issues

#### API Not Running
```
❌ Cannot connect to API: Connection refused
```
**Solution:** Start the API server first:
```bash
python api_server.py
```

#### Import Errors
```
ModuleNotFoundError: No module named 'requests'
```
**Solution:** Install required packages:
```bash
pip install requests
```

#### Test Failures
```
❌ FAIL POST /sources - 500 (expected 200)
```
**Solution:** Check API server logs for errors, verify test data is valid

### Getting Help

1. **Check API server logs** - Look for error messages in the API server console
2. **Verify test data** - Ensure test data matches API expectations
3. **Check network connectivity** - Ensure localhost:8000 is accessible
4. **Review detailed results** - Check `api_test_results.json` for detailed failure information

## 📝 Best Practices

### When to Run Tests
- **Daily:** Quick tests during development
- **Before commits:** Quick tests to catch regressions
- **Before releases:** Comprehensive tests for full validation
- **After API changes:** Relevant test suite for changed functionality

### Interpreting Results
- **100% pass rate:** API is fully functional
- **90%+ pass rate:** API is mostly functional, investigate failures
- **<90% pass rate:** Significant issues, requires investigation

### Test Maintenance
- Update tests when API changes
- Add tests for new features
- Remove tests for deprecated endpoints
- Keep test data realistic but safe

## 🎯 Integration with Development Workflow

### CI/CD Integration
```bash
# In your CI/CD pipeline
python run_api_tests.py --quick
if [ $? -eq 0 ]; then
    echo "API tests passed, proceeding with deployment"
else
    echo "API tests failed, stopping deployment"
    exit 1
fi
```

### Pre-commit Hook
```bash
# In .git/hooks/pre-commit
#!/bin/bash
python api_quick_test.py
exit $?
```

This testing suite ensures your API is always working correctly without manual intervention! 🎉
