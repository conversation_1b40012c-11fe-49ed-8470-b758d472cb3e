[GENERAL]
# General system configuration
debug_mode = false
log_level = INFO
save_output_video = true
output_directory = ./output
enable_gpu = true
gpu_id = 0

[API]
# API Configuration
api_enabled = true
api_host = 0.0.0.0
api_port = 8000
api_workers = 4
allow_cors = true
cors_origins = ["*"]
jwt_secret = your_jwt_secret_key_here
token_expiry_minutes = 60
ssl_enabled = false
ssl_cert = ./ssl/cert.pem
ssl_key = ./ssl/key.pem
upload_folder = ./uploads
max_upload_size_mb = 500

[WEBSOCKET]
# WebSocket configuration
ws_enabled = true
ping_interval = 30
max_connections = 100
message_queue_size = 100
channels = violations,accidents,system

[API_STORAGE]
# API storage paths
upload_dir = uploads
snapshot_dir = snapshots
area_configs_dir = area_configs
violation_dir = Violation_Proc/violations
accident_dir = Violation_Proc/accident_alerts

[VIDEO]
# Video processing settings
default_input_source = 0  # 0 for webcam
resize_width = 1280
resize_height = 720
frame_skip = 0  # Process every frame
buffer_size = 10
max_video_length_seconds = 3600
max_processing_fps = 30
youtube_download_format = mp4
video_extensions = mp4,avi,mkv,mov,webm

[DETECTION]
# Common detection settings
confidence_threshold = 0.5
nms_threshold = 0.45
device = cuda  # or cpu
batch_size = 1

[YOLO]
# YOLOv8 base settings
model_path = ./yolov8s.pt
img_size = 640
classes_to_detect = 0,1,2,3,5,7  # person, bicycle, car, motorcycle, bus, truck

[ACCIDENT_DETECTION]
enabled = true
model_path = ./Models/Accident_det/accident.pt
confidence_threshold = 0.6
log_file = ./Models/Accident_det/accident_log.txt
alert_interval_seconds = 30
persist_alerts = true
persist_format = json,csv

[HELMET_DETECTION]
enabled = true
model_path = ./Models/Bike_Violations/helmet_ds.pt
confidence_threshold = 0.55
save_violations = true
violation_save_path = ./Models/Bike_Violations/violations
logs_directory = ./Models/Bike_Violations/logs
required_confidence_person = 0.6
required_confidence_bike = 0.65
required_confidence_helmet = 0.6
violation_interval_seconds = 5

[PARKING_DETECTION]
enabled = true
stationary_threshold_seconds = 60
confidence_threshold = 0.6
allowed_parking_areas = []  # Empty list means no allowed parking areas
restricted_times = 08:00-20:00  # Format: HH:MM-HH:MM
weekend_exceptions = true

[SPEED_DETECTION]
enabled = true
distance_calibration_factor = 0.1  # meters per pixel
speed_limit_kph = 50
violation_threshold_kph = 55  # Speed to consider as violation
speed_calculation_interval = 5
data_save_path = ./Models/Speed Model/speed_data
pixelsPerMeter = 30  # Calibration factor
detection_line_position = 0.5  # Position of the detection line (0-1 of frame height)

[WRONG_DIRECTION]
enabled = true
log_file = ./Models/Wrong_dir/wrong_direction_log.txt
save_path = ./Models/Wrong_dir/wrong_way_vehicles
direction_tolerance_angle = 30
min_displacement_pixels = 20
track_frames_threshold = 10

[TRAFFIC_VIOLATION]
enabled = true
red_light_violation = true
stop_sign_violation = true
no_entry_violation = true
one_way_violation = true
use_masks = true
mask_path = ./Models/Traffic_violation/masks
violation_save_path = ./Violation_Proc/violations
red_light_wait_time = 5

[LICENSE_RECOGNITION]
enabled = true
model_path = ./Violation_Proc/License_recognition/Licenses.pt  
confidence_threshold = 0.6
save_detections = true
save_path = ./Violation_Proc/violations/plates
min_plate_width = 30
min_plate_height = 15
ocr_languages = en

[AREA_DEFINITION]
area_config_file = ./area_configs/areas_video2.json
use_polygon_areas = true
draw_areas_on_frame = true
area_opacity = 0.3

[NOTIFICATION]
email_enabled = false
email_smtp_server = smtp.example.com
email_smtp_port = 587
email_from = <EMAIL>
email_recipients = <EMAIL>
email_username = your_username
email_password = your_password
email_use_tls = true

sms_enabled = false
sms_provider = twilio
sms_account_sid = your_account_sid
sms_auth_token = your_auth_token
sms_from_number = +**********
sms_to_numbers = +**********

webhook_enabled = false
webhook_url = https://example.com/webhook
webhook_method = POST
webhook_headers = {"Content-Type": "application/json", "X-API-Key": "your-api-key"}

[DATABASE]
enabled = true
type = sqlite  # sqlite, mysql, postgresql
sqlite_path = ./violations.db
connection_string = sqlite:///violations.db
pool_size = 5
pool_recycle = 3600
mysql_host = localhost
mysql_port = 3306
mysql_database = violations_db
mysql_user = db_user
mysql_password = db_password

[MULTIPROCESSING]
enabled = true
max_workers = 4
queue_size = 100
