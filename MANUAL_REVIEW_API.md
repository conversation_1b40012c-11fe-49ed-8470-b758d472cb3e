# Manual Review API Documentation

## Overview

The Manual Review API provides functionality to handle violations where license plate recognition has failed or has low confidence. When the license plate recognition system cannot read a license plate text or has very low confidence (< 0.3), a manual review entry is automatically created for human review.

## Automatic Manual Review Creation

Manual reviews are automatically created when:
- License plate recognition completely fails (empty text)
- License plate recognition confidence is below 0.3
- The system detects a license plate but cannot read the text

When a manual review is completed with a corrected license plate, the system automatically:
- Updates the original violation record in both JSON and CSV databases
- Sets `manual_review_completed: true` on the violation
- Adds `manual_review_date` timestamp
- Updates the `license_plate` field with the corrected value

## API Endpoints

### 1. Create Manual Review
**POST** `/api/manual-reviews`

Create a new manual review entry for a violation.

**Request Body:**
```json
{
  "violation_id": "string",
  "reason": "string (optional, default: 'License plate recognition failed')",
  "notes": "string (optional)"
}
```

**Response:**
```json
{
  "review_id": "MR_12345678",
  "violation_id": "000001_abc123",
  "violation_data": { ... },
  "reason": "License plate recognition failed",
  "notes": "Optional notes",
  "license_plate": null,
  "reviewer_name": null,
  "created_at": "2024-01-01T12:00:00",
  "updated_at": null,
  "status": "pending"
}
```

### 2. Get All Manual Reviews
**GET** `/api/manual-reviews`

Get all manual reviews without filtering.

**Response:** Array of manual review objects

### 3. Search Manual Reviews
**POST** `/api/manual-reviews/search`

Get manual reviews with optional filtering.

**Request Body:**
```json
{
  "status": "pending|completed|cancelled (optional)",
  "start_date": "YYYY-MM-DD (optional)",
  "end_date": "YYYY-MM-DD (optional)",
  "violation_type": "string (optional)",
  "source_id": "string (optional)"
}
```

### 4. Get Specific Manual Review
**GET** `/api/manual-reviews/{review_id}`

Get details for a specific manual review.

### 5. Update Manual Review
**PUT** `/api/manual-reviews/{review_id}`

Update a manual review with corrected license plate information.

**Request Body:**
```json
{
  "license_plate": "ABC123",
  "notes": "Manually corrected license plate",
  "reviewer_name": "John Doe"
}
```

When a license plate is provided, the status automatically changes to "completed" and the original violation record is updated.

### 6. Delete Manual Review
**DELETE** `/api/manual-reviews/{review_id}`

Delete a manual review entry.

### 7. Get Manual Review Statistics
**GET** `/api/manual-reviews/statistics`

Get statistics about manual reviews.

**Response:**
```json
{
  "total": 10,
  "by_status": {
    "pending": 5,
    "completed": 4,
    "cancelled": 1
  },
  "by_violation_type": {
    "traffic_light": 3,
    "speed": 2,
    "parking": 5
  },
  "by_source": {
    "camera_001": 6,
    "camera_002": 4
  },
  "by_date": {
    "2024-01-01": 3,
    "2024-01-02": 7
  }
}
```

## Media Endpoints

### Get License Plate Snapshot for Manual Review
**GET** `/api/media/manual-reviews/{review_id}/plate`

Returns the license plate image for manual review.

### Get Violation Snapshot for Manual Review
**GET** `/api/media/manual-reviews/{review_id}/violation`

Returns the violation snapshot image for manual review.

## Manual Review Workflow

1. **Automatic Creation**: When license plate recognition fails, a manual review is automatically created with status "pending"

2. **Review Process**: 
   - Reviewer accesses the manual review via API
   - Views the license plate image and violation snapshot
   - Manually identifies the license plate text

3. **Update**: Reviewer updates the manual review with the correct license plate text

4. **Completion**: When license plate is provided, the status changes to "completed" and the original violation record is updated

## Status Values

- **pending**: Manual review is waiting for human review
- **completed**: Manual review has been completed with license plate correction
- **cancelled**: Manual review has been cancelled (not needed)

## Integration Example

```python
import requests

# Get pending manual reviews
response = requests.post("http://localhost:8000/api/manual-reviews/search", 
                        json={"status": "pending"})
pending_reviews = response.json()

for review in pending_reviews:
    review_id = review['review_id']
    violation_id = review['violation_id']
    
    # Get license plate image
    plate_image_url = f"http://localhost:8000/api/media/manual-reviews/{review_id}/plate"
    
    # After manual review, update with correct license plate
    update_data = {
        "license_plate": "ABC123",
        "reviewer_name": "Human Reviewer",
        "notes": "Manually corrected"
    }
    
    requests.put(f"http://localhost:8000/api/manual-reviews/{review_id}", 
                json=update_data)
```

## Database Structure

### Violation Records
Violations are stored in:
- **JSON**: `Violation_Proc/violations/unified_violations.json`
- **CSV**: `Violation_Proc/violations/unified_violations.csv`

Key fields in violation records:
```json
{
  "violation_id": "000001_abc123",
  "license_plate": "ABC123",
  "snapshot_path": "snapshots\\violation_snapshot.jpg",
  "plate_snapshot_path": "plates\\plate_snapshot.jpg",
  "manual_review_completed": true,
  "manual_review_date": "2024-01-01T12:00:00"
}
```

### Manual Review Records
Manual reviews are stored in:
- **JSON**: `Violation_Proc/violations/manual_reviews.json`

### Media Files
- **Violation snapshots**: `Violation_Proc/violations/snapshots/`
- **License plate images**: `Violation_Proc/violations/plates/`

## Error Handling

All endpoints return appropriate HTTP status codes:
- 200: Success
- 400: Bad request (invalid data)
- 404: Resource not found
- 500: Internal server error

Error responses include a detail message explaining the issue.

## Testing

Use the provided test scripts:
- `test_manual_review.py` - Comprehensive API testing
- `demo_manual_review.py` - Complete workflow demonstration
