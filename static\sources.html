<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Sources - Traffic Monitoring System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Traffic Monitoring System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sources.html">Video Sources</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="config.html">Configuration</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between">
                        <h5>Video Sources</h5>
                        <button class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                            <i class="fas fa-plus"></i> Add Source
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="videoSourcesList" class="row">
                            <p class="text-center text-muted">Loading video sources...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Source Modal -->
    <div class="modal fade" id="addSourceModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Video Source</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSourceForm">
                        <div class="mb-3">
                            <label class="form-label">Source Name</label>
                            <input type="text" class="form-control" id="sourceName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Source URL/Path</label>
                            <input type="text" class="form-control" id="sourceUrl" required>
                            <small class="text-muted">Local file path or stream URL (RTSP/HTTP)</small>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="useStream">
                            <label class="form-check-label">Is Live Stream</label>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control" id="sourceLocation" value="Unknown">
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Latitude</label>
                                <input type="number" class="form-control" id="sourceLat" value="0.0" step="0.000001">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Longitude</label>
                                <input type="number" class="form-control" id="sourceLng" value="0.0" step="0.000001">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Speed Limit (km/h)</label>
                            <input type="number" class="form-control" id="speedLimit" value="60.0" step="0.1">
                        </div>
                        <button type="submit" class="btn btn-primary">Add Source</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize sources page
            fetchVideoSources();
            setupAddSourceForm();
        });

        function setupAddSourceForm() {
            const form = document.getElementById('addSourceForm');
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const newSource = {
                    name: document.getElementById('sourceName').value,
                    source: document.getElementById('sourceUrl').value,
                    use_stream: document.getElementById('useStream').checked,
                    location: document.getElementById('sourceLocation').value,
                    coordinates: {
                        lat: parseFloat(document.getElementById('sourceLat').value),
                        lng: parseFloat(document.getElementById('sourceLng').value)
                    },
                    speed_limit: parseFloat(document.getElementById('speedLimit').value)
                };

                addVideoSource(newSource);
            });
        }
    </script>
</body>
</html>
