/* Main styles for Traffic Monitoring System */

body {
    background-color: #f5f5f5;
}

.navbar-brand {
    font-weight: 700;
}

.card {
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: 600;
}

/* Source card styles */
.source-card {
    transition: all 0.3s ease;
}

.source-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.source-status {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
}

.status-running {
    background-color: #28a745;
}

.status-stopped {
    background-color: #dc3545;
}

.status-error {
    background-color: #ffc107;
}

/* Canvas styles */
.canvas-container {
    position: relative;
    margin: 0 auto;
    background-color: #000;
    overflow: hidden;
}

/* Area drawing colors */
.area-detection {
    stroke: rgb(0, 255, 0);
    fill: rgba(0, 255, 0, 0.2);
}

.area-speed {
    stroke: rgb(255, 0, 0);
    fill: rgba(255, 0, 0, 0.2);
}

.area-wrong_direction {
    stroke: rgb(0, 0, 255);
    fill: rgba(0, 0, 255, 0.2);
}

.area-parking {
    stroke: rgb(255, 255, 0);
    fill: rgba(255, 255, 0, 0.2);
}

.area-traffic_line {
    stroke: rgb(255, 0, 255);
    fill: rgba(255, 0, 255, 0.2);
}

.area-traffic_sign {
    stroke: rgb(0, 255, 255);
    fill: rgba(0, 255, 255, 0.2);
}

.area-custom {
    stroke: rgb(128, 128, 128);
    fill: rgba(128, 128, 128, 0.2);
}

/* Line styles */
.line-speed, .line-wrong_direction, .line-traffic_line {
    stroke-width: 2;
}

/* Model Status Buttons */
.model-status-btn {
    transition: all 0.3s ease;
    border: 2px solid;
    font-weight: 500;
    position: relative;
}

.model-status-btn.active {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.model-status-btn.inactive {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.model-status-btn .badge {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
    font-weight: 600;
}

.model-status-btn.active .badge {
    background-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

.model-status-btn.inactive .badge {
    background-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

/* Hover effects */
.model-status-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.model-status-btn:active {
    transform: translateY(0);
}

/* Loading state */
.model-status-btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

.model-status-btn.loading .badge::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: ""; }
    40% { content: "."; }
    60% { content: ".."; }
    80%, 100% { content: "..."; }
}
