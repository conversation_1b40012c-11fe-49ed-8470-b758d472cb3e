#!/usr/bin/env python3
"""
Quick API Test - Tests the most important endpoints
Use this for quick validation that the API is working correctly
"""

import requests
import json
import uuid
from datetime import datetime

BASE_URL = "http://localhost:8000/api"

def test_api_status():
    """Test if API is running"""
    print("🔍 Testing API Status...")
    try:
        response = requests.get(f"{BASE_URL}")
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {str(e)}")
        return False

def test_core_endpoints():
    """Test core functionality endpoints"""
    print("\n🎯 Testing Core Endpoints...")
    
    tests = [
        # Basic endpoints
        ("GET", "", "API Root"),
        ("GET", "/sources", "List Video Sources"),
        ("GET", "/violations", "Get Violations"),
        ("GET", "/manual-reviews", "Get Manual Reviews"),
        ("GET", "/system/status", "System Status"),
        
        # Data endpoints with filters
        ("POST", "/violations", "Filtered Violations"),
        ("POST", "/accidents", "Filtered Accidents"),
        ("GET", "/accidents/statistics", "Accident Statistics"),
    ]
    
    results = []
    
    for method, endpoint, description in tests:
        try:
            url = f"{BASE_URL}{endpoint}"
            
            if method == "GET":
                response = requests.get(url)
            elif method == "POST":
                # Send empty filter for POST endpoints
                response = requests.post(url, json={})
            
            success = response.status_code in [200, 404]  # 404 is OK for empty data
            status = "✅" if success else "❌"
            
            print(f"{status} {method} {endpoint} - {response.status_code} ({description})")
            
            results.append({
                "endpoint": endpoint,
                "method": method,
                "status": response.status_code,
                "success": success,
                "description": description
            })
            
        except Exception as e:
            print(f"❌ {method} {endpoint} - ERROR: {str(e)}")
            results.append({
                "endpoint": endpoint,
                "method": method,
                "status": 0,
                "success": False,
                "description": description,
                "error": str(e)
            })
    
    return results

def test_manual_review_workflow():
    """Test the complete manual review workflow"""
    print("\n📝 Testing Manual Review Workflow...")
    
    # Create test violation data
    violation_data = {
        "date": datetime.now().strftime('%Y-%m-%d'),
        "time": datetime.now().strftime('%H:%M:%S'),
        "license_plate": "",  # Empty - needs manual review
        "camera_id": "test_camera",
        "camera_location": "Test Location",
        "violation_type": "speed",
        "violation_id": f"QUICK_TEST_{uuid.uuid4().hex[:6]}",
        "snapshot_path": "snapshots/test.jpg",
        "plate_snapshot_path": "plates/test.jpg",
        "extra_info": {"speed": 75.0}
    }
    
    try:
        # Step 1: Create manual review
        print("   1. Creating manual review...")
        create_data = {
            "violation_data": violation_data,
            "reason": "Quick test - license plate recognition failed",
            "notes": "Created by quick test script"
        }
        
        response = requests.post(f"{BASE_URL}/manual-reviews", json=create_data)
        if response.status_code == 200:
            review = response.json()
            review_id = review['review_id']
            violation_id = violation_data['violation_id']
            print(f"   ✅ Created manual review: {review_id}")
            
            # Step 2: Get the manual review
            print("   2. Retrieving manual review...")
            response = requests.get(f"{BASE_URL}/manual-reviews/{review_id}")
            if response.status_code == 200:
                print("   ✅ Retrieved manual review successfully")
                
                # Step 3: Update manual review
                print("   3. Updating manual review with license plate...")
                update_data = {
                    "license_plate": "QUICK123",
                    "notes": "Updated by quick test",
                    "reviewer_name": "Quick Tester"
                }
                
                response = requests.put(f"{BASE_URL}/manual-reviews/{review_id}", json=update_data)
                if response.status_code == 200:
                    print("   ✅ Updated manual review successfully")
                    
                    # Step 4: Verify violation was added to database
                    print("   4. Verifying violation was added to database...")
                    response = requests.get(f"{BASE_URL}/violations/{violation_id}")
                    if response.status_code == 200:
                        violation = response.json()
                        if violation.get('license_plate') == 'QUICK123':
                            print("   ✅ Violation successfully added to database with correct license plate")
                        else:
                            print("   ❌ Violation license plate not updated correctly")
                    else:
                        print(f"   ❌ Violation not found in database (status: {response.status_code})")
                    
                    # Step 5: Clean up
                    print("   5. Cleaning up test data...")
                    response = requests.delete(f"{BASE_URL}/manual-reviews/{review_id}")
                    if response.status_code == 200:
                        print("   ✅ Test manual review deleted successfully")
                    else:
                        print(f"   ⚠️  Could not delete test manual review (status: {response.status_code})")
                        
                else:
                    print(f"   ❌ Failed to update manual review (status: {response.status_code})")
            else:
                print(f"   ❌ Failed to retrieve manual review (status: {response.status_code})")
        else:
            print(f"   ❌ Failed to create manual review (status: {response.status_code})")
            
    except Exception as e:
        print(f"   ❌ Error in manual review workflow: {str(e)}")

def test_error_handling():
    """Test error handling for invalid requests"""
    print("\n🚫 Testing Error Handling...")
    
    error_tests = [
        ("GET", "/sources/invalid_source_123", 404, "Non-existent video source"),
        ("GET", "/violations/invalid_violation_123", 404, "Non-existent violation"),
        ("GET", "/manual-reviews/invalid_review_123", 404, "Non-existent manual review"),
        ("GET", "/nonexistent/endpoint", 404, "Non-existent endpoint"),
    ]
    
    for method, endpoint, expected_status, description in error_tests:
        try:
            url = f"{BASE_URL}{endpoint}"
            response = requests.get(url)
            
            if response.status_code == expected_status:
                print(f"   ✅ {description} - correctly returned {response.status_code}")
            else:
                print(f"   ❌ {description} - expected {expected_status}, got {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {description} - Error: {str(e)}")

def run_quick_test():
    """Run all quick tests"""
    print("🚀 Quick API Test Suite")
    print("=" * 50)
    
    # Test API status first
    if not test_api_status():
        print("\n❌ API is not accessible. Please start the API server first:")
        print("   python api_server.py")
        return False
    
    # Run core tests
    core_results = test_core_endpoints()
    
    # Test manual review workflow
    test_manual_review_workflow()
    
    # Test error handling
    test_error_handling()
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 50)
    
    total_core_tests = len(core_results)
    passed_core_tests = sum(1 for result in core_results if result["success"])
    
    print(f"Core Endpoint Tests: {passed_core_tests}/{total_core_tests} passed")
    
    if passed_core_tests == total_core_tests:
        print("✅ All core endpoints are working correctly!")
    else:
        print("❌ Some core endpoints have issues:")
        for result in core_results:
            if not result["success"]:
                print(f"   - {result['method']} {result['endpoint']} ({result['description']})")
    
    print("\n✅ Quick test completed!")
    print("💡 For comprehensive testing, run: python api_comprehensive_test.py")
    
    return True

if __name__ == "__main__":
    run_quick_test()
