#!/usr/bin/env python3
"""
API Test Runner
Provides options to run different types of API tests
"""

import sys
import subprocess
import argparse
from datetime import datetime

def run_test_script(script_name):
    """Run a test script and return the result"""
    try:
        print(f"\n🚀 Running {script_name}...")
        print("=" * 60)
        
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True)
        
        if result.returncode == 0:
            print(f"\n✅ {script_name} completed successfully")
            return True
        else:
            print(f"\n❌ {script_name} failed with return code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ Error running {script_name}: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="API Test Runner")
    parser.add_argument("--quick", "-q", action="store_true", 
                       help="Run quick tests only")
    parser.add_argument("--comprehensive", "-c", action="store_true", 
                       help="Run comprehensive tests only")
    parser.add_argument("--manual-review", "-m", action="store_true", 
                       help="Run manual review tests only")
    parser.add_argument("--all", "-a", action="store_true", 
                       help="Run all available tests")
    
    args = parser.parse_args()
    
    print("🧪 API Test Runner")
    print(f"Started at: {datetime.now().isoformat()}")
    print("=" * 60)
    
    results = []
    
    # If no specific test is requested, run quick test by default
    if not any([args.quick, args.comprehensive, args.manual_review, args.all]):
        args.quick = True
    
    if args.quick or args.all:
        print("\n📋 Running Quick API Tests...")
        success = run_test_script("api_quick_test.py")
        results.append(("Quick Tests", success))
    
    if args.comprehensive or args.all:
        print("\n📋 Running Comprehensive API Tests...")
        success = run_test_script("api_comprehensive_test.py")
        results.append(("Comprehensive Tests", success))
    
    if args.manual_review or args.all:
        print("\n📋 Running Manual Review Tests...")
        success = run_test_script("simple_manual_review_test.py")
        results.append(("Manual Review Tests", success))
    
    # Print final summary
    print("\n" + "=" * 60)
    print("🏁 FINAL TEST SUMMARY")
    print("=" * 60)
    
    total_suites = len(results)
    passed_suites = sum(1 for _, success in results if success)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed_suites}/{total_suites} test suites passed")
    
    if passed_suites == total_suites:
        print("🎉 All test suites completed successfully!")
        return 0
    else:
        print("⚠️  Some test suites failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
