[{"endpoint": "", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:32.994031"}, {"endpoint": "/sources", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:35.045422"}, {"endpoint": "/sources", "method": "POST", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:06:37.098601"}, {"endpoint": "/sources", "method": "POST", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:39.207162"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:41.250380"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/start", "method": "POST", "status_code": 500, "expected": 500, "success": true, "details": "", "timestamp": "2025-06-14T02:06:43.307886"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/stop", "method": "POST", "status_code": 200, "expected": 500, "success": false, "details": "", "timestamp": "2025-06-14T02:06:45.337168"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/areas", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:47.365919"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/areas", "method": "PUT", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:49.416645"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/areas/clear-all", "method": "POST", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:51.464030"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/settings", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:53.519389"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/settings", "method": "PUT", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:55.567638"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e/frame", "method": "GET", "status_code": 500, "expected": 500, "success": true, "details": "", "timestamp": "2025-06-14T02:06:57.623493"}, {"endpoint": "/sources/e64ddeb1-4c06-4432-b62e-d80f625e386e", "method": "DELETE", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:06:59.664572"}, {"endpoint": "/violations", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:01.690662"}, {"endpoint": "/violations", "method": "POST", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:03.736828"}, {"endpoint": "/violations/types/speed", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:05.785548"}, {"endpoint": "/violations/test_violation_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:07.829719"}, {"endpoint": "/manual-reviews", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:09.865836"}, {"endpoint": "/manual-reviews", "method": "POST", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:11.928480"}, {"endpoint": "/manual-reviews/MR_a0141c0b", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:13.995376"}, {"endpoint": "/manual-reviews/MR_a0141c0b", "method": "PUT", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:16.119513"}, {"endpoint": "/media/manual-reviews/MR_a0141c0b/plate", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:18.272194"}, {"endpoint": "/media/manual-reviews/MR_a0141c0b/violation", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:20.336061"}, {"endpoint": "/manual-reviews/MR_a0141c0b", "method": "DELETE", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:22.712793"}, {"endpoint": "/accidents", "method": "POST", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:24.762296"}, {"endpoint": "/accidents/statistics", "method": "GET", "status_code": 404, "expected": 200, "success": false, "details": "", "timestamp": "2025-06-14T02:07:26.818984"}, {"endpoint": "/media/violations/test_violation_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:28.887152"}, {"endpoint": "/media/plates/test_violation_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:30.950456"}, {"endpoint": "/media/accidents/test_accident_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:32.997414"}, {"endpoint": "/system/status", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:07:35.053256"}, {"endpoint": "/nonexistent", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:37.097859"}, {"endpoint": "/invalid/endpoint", "method": "POST", "status_code": 405, "expected": 404, "success": false, "details": "", "timestamp": "2025-06-14T02:07:39.151984"}, {"endpoint": "/sources/invalid_source_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:41.209652"}, {"endpoint": "/sources/invalid_source_123/start", "method": "POST", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:43.263172"}, {"endpoint": "/sources/invalid_source_123", "method": "DELETE", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:45.310177"}, {"endpoint": "/violations/invalid_violation_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:47.343608"}, {"endpoint": "/manual-reviews/invalid_review_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:49.373603"}, {"endpoint": "/manual-reviews/invalid_review_123", "method": "PUT", "status_code": 400, "expected": 404, "success": false, "details": "", "timestamp": "2025-06-14T02:07:51.409758"}, {"endpoint": "/manual-reviews/invalid_review_123", "method": "DELETE", "status_code": 400, "expected": 404, "success": false, "details": "", "timestamp": "2025-06-14T02:07:53.458685"}, {"endpoint": "/media/violations/invalid_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:55.526947"}, {"endpoint": "/media/plates/invalid_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:57.568768"}, {"endpoint": "/media/accidents/invalid_123", "method": "GET", "status_code": 404, "expected": 404, "success": true, "details": "", "timestamp": "2025-06-14T02:07:59.611812"}, {"endpoint": "/manual-reviews", "method": "POST", "status_code": 422, "expected": 422, "success": true, "details": "", "timestamp": "2025-06-14T02:08:01.684486"}, {"endpoint": "/sources", "method": "POST", "status_code": 422, "expected": 422, "success": true, "details": "", "timestamp": "2025-06-14T02:08:03.741317"}, {"endpoint": "/sources", "method": "POST", "status_code": 404, "expected": 422, "success": false, "details": "", "timestamp": "2025-06-14T02:08:05.793280"}, {"endpoint": "/sources", "method": "POST", "status_code": 422, "expected": 422, "success": true, "details": "", "timestamp": "2025-06-14T02:08:07.851999"}, {"endpoint": "/sources", "method": "POST", "status_code": 422, "expected": 422, "success": true, "details": "", "timestamp": "2025-06-14T02:08:09.901065"}, {"endpoint": "/violations", "method": "GET", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:08:11.966401"}, {"endpoint": "/accidents", "method": "POST", "status_code": 200, "expected": 200, "success": true, "details": "", "timestamp": "2025-06-14T02:08:13.994775"}]