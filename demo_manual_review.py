#!/usr/bin/env python3
"""
Demo script showing the complete manual review workflow
This demonstrates how manual reviews are created automatically and how to process them
"""

import requests
import json
import time
import uuid
from datetime import datetime

# API base URL
BASE_URL = "http://localhost:8000/api"

def demo_manual_review_workflow():
    """Demonstrate the complete manual review workflow"""
    print("🚗 Manual Review Workflow Demonstration")
    print("=" * 60)
    
    # Step 1: Check for existing violations with empty license plates
    print("\n📋 Step 1: Finding violations that need manual review")
    try:
        response = requests.get(f"{BASE_URL}/violations")
        if response.status_code == 200:
            violations = response.json()
            empty_plate_violations = [v for v in violations if not v.get('license_plate') or v.get('license_plate').strip() == '']
            
            print(f"Found {len(violations)} total violations")
            print(f"Found {len(empty_plate_violations)} violations with empty license plates")
            
            if empty_plate_violations:
                # Show some examples
                for i, violation in enumerate(empty_plate_violations[:3]):
                    print(f"  - Violation {violation.get('violation_id')}: {violation.get('violation_type')} at {violation.get('time')}")
                    
                    # Check if plate snapshot exists
                    plate_path = violation.get('plate_snapshot_path')
                    if plate_path:
                        print(f"    📸 Plate snapshot: {plate_path}")
                    else:
                        print(f"    ❌ No plate snapshot available")
            
        else:
            print(f"Error getting violations: {response.text}")
            return
    except Exception as e:
        print(f"Error: {str(e)}")
        return
    
    # Step 2: Check for existing manual reviews
    print("\n📝 Step 2: Checking existing manual reviews")
    try:
        response = requests.get(f"{BASE_URL}/manual-reviews")
        if response.status_code == 200:
            reviews = response.json()
            print(f"Found {len(reviews)} existing manual reviews")
            
            pending_reviews = [r for r in reviews if r.get('status') == 'pending']
            completed_reviews = [r for r in reviews if r.get('status') == 'completed']
            
            print(f"  - Pending: {len(pending_reviews)}")
            print(f"  - Completed: {len(completed_reviews)}")
            
            if pending_reviews:
                print("\nPending reviews:")
                for review in pending_reviews[:3]:
                    print(f"  - Review {review.get('review_id')}: {review.get('reason')}")
                    
        else:
            print(f"Error getting manual reviews: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Step 3: Create a manual review for demonstration
    if empty_plate_violations:
        test_violation = empty_plate_violations[0]
        violation_id = test_violation.get('violation_id')
        
        print(f"\n🔧 Step 3: Creating manual review for violation {violation_id}")
        
        manual_review_data = {
            "violation_id": violation_id,
            "reason": "Demo: License plate recognition failed",
            "notes": "Created by demo script for testing purposes"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/manual-reviews", json=manual_review_data)
            if response.status_code == 200:
                created_review = response.json()
                review_id = created_review.get('review_id')
                print(f"✅ Created manual review: {review_id}")
                print(f"   Status: {created_review.get('status')}")
                print(f"   Reason: {created_review.get('reason')}")
                
                # Step 4: Access media files
                print(f"\n📸 Step 4: Accessing media files for review {review_id}")
                
                # Try to access violation snapshot
                try:
                    response = requests.get(f"{BASE_URL}/media/manual-reviews/{review_id}/violation")
                    if response.status_code == 200:
                        print("✅ Violation snapshot accessible")
                    else:
                        print(f"❌ Violation snapshot not accessible: {response.status_code}")
                except Exception as e:
                    print(f"Error accessing violation snapshot: {str(e)}")
                
                # Try to access plate snapshot
                try:
                    response = requests.get(f"{BASE_URL}/media/manual-reviews/{review_id}/plate")
                    if response.status_code == 200:
                        print("✅ Plate snapshot accessible")
                    else:
                        print(f"❌ Plate snapshot not accessible: {response.status_code}")
                except Exception as e:
                    print(f"Error accessing plate snapshot: {str(e)}")
                
                # Step 5: Simulate manual review completion
                print(f"\n✏️  Step 5: Completing manual review with corrected license plate")
                
                update_data = {
                    "license_plate": "DEMO123",
                    "notes": "Manually corrected by demo script",
                    "reviewer_name": "Demo Reviewer"
                }
                
                try:
                    response = requests.put(f"{BASE_URL}/manual-reviews/{review_id}", json=update_data)
                    if response.status_code == 200:
                        updated_review = response.json()
                        print(f"✅ Manual review completed")
                        print(f"   License plate: {updated_review.get('license_plate')}")
                        print(f"   Status: {updated_review.get('status')}")
                        print(f"   Reviewer: {updated_review.get('reviewer_name')}")
                        
                        # Step 6: Verify original violation was updated
                        print(f"\n🔍 Step 6: Verifying original violation was updated")
                        
                        try:
                            response = requests.get(f"{BASE_URL}/violations/{violation_id}")
                            if response.status_code == 200:
                                updated_violation = response.json()
                                original_plate = updated_violation.get('license_plate')
                                manual_completed = updated_violation.get('manual_review_completed')
                                manual_date = updated_violation.get('manual_review_date')
                                
                                print(f"   Original violation license plate: {original_plate}")
                                print(f"   Manual review completed: {manual_completed}")
                                print(f"   Manual review date: {manual_date}")
                                
                                if original_plate == "DEMO123" and manual_completed:
                                    print("✅ SUCCESS: Original violation properly updated in database!")
                                else:
                                    print("❌ FAILED: Original violation not properly updated")
                            else:
                                print(f"Error getting updated violation: {response.text}")
                        except Exception as e:
                            print(f"Error verifying violation update: {str(e)}")
                        
                        # Step 7: Clean up - delete the demo manual review
                        print(f"\n🧹 Step 7: Cleaning up demo manual review")
                        try:
                            response = requests.delete(f"{BASE_URL}/manual-reviews/{review_id}")
                            if response.status_code == 200:
                                print("✅ Demo manual review deleted")
                            else:
                                print(f"❌ Error deleting demo review: {response.text}")
                        except Exception as e:
                            print(f"Error deleting demo review: {str(e)}")
                        
                    else:
                        print(f"Error updating manual review: {response.text}")
                except Exception as e:
                    print(f"Error: {str(e)}")
                
            else:
                print(f"Error creating manual review: {response.text}")
        except Exception as e:
            print(f"Error: {str(e)}")
    else:
        print("\n⚠️  No violations with empty license plates found for demonstration")

    # Step 3.1: Demonstrate new workflow - create manual review with violation data
    print(f"\n🆕 Step 3.1: Demonstrating NEW workflow - Manual review with violation data")

    # Create a sample violation data that would normally go to manual review
    sample_violation_data = {
        "date": datetime.now().strftime('%Y-%m-%d'),
        "time": datetime.now().strftime('%H:%M:%S'),
        "license_plate": "",  # Empty - this is why it goes to manual review
        "camera_id": "demo_camera",
        "camera_location": "Demo Location",
        "location_coordinates": {"lat": 0.0, "lng": 0.0},
        "violation_type": "speed",
        "violation_id": f"DEMO_{uuid.uuid4().hex[:6]}",
        "snapshot_path": "snapshots\\demo_violation.jpg",
        "extra_info": {"speed": 75.5},
        "plate_snapshot_path": "plates\\demo_plate.jpg"
    }

    manual_review_with_violation_data = {
        "violation_data": sample_violation_data,
        "reason": "NEW WORKFLOW: License plate recognition failed - creating manual review instead of violation",
        "notes": "This demonstrates the new workflow where violations with empty plates go directly to manual review"
    }

    try:
        response = requests.post(f"{BASE_URL}/manual-reviews/with-violation", json=manual_review_with_violation_data)
        if response.status_code == 200:
            created_review = response.json()
            review_id = created_review.get('review_id')
            violation_id = sample_violation_data.get('violation_id')

            print(f"✅ Created manual review with violation data: {review_id}")
            print(f"   Violation ID: {violation_id}")
            print(f"   Status: {created_review.get('status')}")

            # Complete the manual review
            print(f"\n✏️  Completing manual review with corrected license plate")

            update_data = {
                "license_plate": "NEW123",
                "notes": "Corrected using NEW workflow",
                "reviewer_name": "New Workflow Demo"
            }

            try:
                response = requests.put(f"{BASE_URL}/manual-reviews/{review_id}", json=update_data)
                if response.status_code == 200:
                    updated_review = response.json()
                    print(f"✅ Manual review completed")
                    print(f"   License plate: {updated_review.get('license_plate')}")
                    print(f"   Status: {updated_review.get('status')}")

                    # Verify the violation was added to the database
                    print(f"\n🔍 Verifying violation was added to database")

                    try:
                        response = requests.get(f"{BASE_URL}/violations/{violation_id}")
                        if response.status_code == 200:
                            violation = response.json()
                            print(f"✅ SUCCESS: Violation now exists in database!")
                            print(f"   License plate: {violation.get('license_plate')}")
                            print(f"   Manual review completed: {violation.get('manual_review_completed')}")
                        else:
                            print(f"❌ Violation not found in database: {response.status_code}")
                    except Exception as e:
                        print(f"Error checking violation: {str(e)}")

                    # Clean up
                    print(f"\n🧹 Cleaning up new workflow demo")
                    try:
                        response = requests.delete(f"{BASE_URL}/manual-reviews/{review_id}")
                        if response.status_code == 200:
                            print("✅ Demo manual review deleted")
                    except Exception as e:
                        print(f"Error deleting demo review: {str(e)}")

                else:
                    print(f"Error updating manual review: {response.text}")
            except Exception as e:
                print(f"Error: {str(e)}")

        else:
            print(f"Error creating manual review with violation data: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")

    # Step 8: Show statistics
    print(f"\n📊 Step 8: Manual Review Statistics")
    try:
        response = requests.get(f"{BASE_URL}/manual-reviews/statistics")
        if response.status_code == 200:
            stats = response.json()
            print(f"Total manual reviews: {stats.get('total', 0)}")
            
            by_status = stats.get('by_status', {})
            for status, count in by_status.items():
                print(f"  - {status.capitalize()}: {count}")
                
        else:
            print(f"Error getting statistics: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")

def check_api_connection():
    """Check if API server is running"""
    try:
        response = requests.get(f"{BASE_URL}")
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print("❌ API server returned error")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {str(e)}")
        print("Make sure the API server is running on http://localhost:8000")
        return False

if __name__ == "__main__":
    print("🚀 Manual Review System Demo")
    print("=" * 40)
    print(f"Time: {datetime.now().isoformat()}")
    print(f"API URL: {BASE_URL}")
    
    if check_api_connection():
        demo_manual_review_workflow()
    
    print("\n🎉 Demo completed!")
