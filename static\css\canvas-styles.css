/* Canvas styles for proper video scaling */
.canvas-container {
    margin-bottom: 20px !important;
    position: relative;
    display: block;
    overflow: hidden;
    min-height: 600px;
    width: 100% !important;
    max-width: 1280px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.25);
    background-color: #000; /* Add black background so borders are visible */
}

@media (min-width: 1200px) {
    .canvas-container {
        min-height: 700px;
    }
}

@media (max-width: 768px) {
    .canvas-container {
        min-height: 450px;
    }
}

.canvas-container canvas {
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    position: absolute; /* Ensure canvas elements are properly positioned */
    top: 0;
    left: 0;
}

/* For upper and lower canvas synchronization */
.upper-canvas {
    width: 100% !important;
    height: 100% !important;
    z-index: 1; /* Ensure upper canvas is above lower canvas */
}

.lower-canvas {
    width: 100% !important;
    height: 100% !important;
    z-index: 0; /* Ensure lower canvas is below upper canvas */
}

#videoCanvas {
    /* Ensure the canvas renders properly and scaling calculation work correctly */
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Add loading indicator for video */
.canvas-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    background-color: rgba(0,0,0,0.7);
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 2;
}

/* Improve visibility of canvas controls */
.canvas-controls {
    position: absolute;
    bottom: 10px;
    left: 10px;
    z-index: 10;
    background-color: rgba(0,0,0,0.6);
    padding: 5px;
    border-radius: 4px;
}

/* Ensure toast notifications are visible */
#toast-container {
    z-index: 2000 !important;
}

/* Hidden coordinate calibration styles - used by the system but not visible to users */
.coordinate-hidden {
    display: none !important;
}

/* Canvas loading indicator */
.canvas-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0,0,0,0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-family: sans-serif;
    z-index: 1000;
}